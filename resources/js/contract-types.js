/**
 * Contract Types Management JavaScript
 * Handles CRUD operations for contract types with AJAX
 */

// Global variables
let contractTypesTable;
let deleteContractTypeId = null;
let isEditMode = false;

// Initialize when DOM is ready
$(document).ready(function() {
  // Check if routes are available
  if (!checkRoutes(window.contractTypesRoutes, 'Contract Types')) {
    return;
  }

  initializeDataTable();
  initializeEventHandlers();
});

/**
 * Initialize DataTable for contract types
 */
function initializeDataTable() {
  contractTypesTable = $('#contractTypesTable').DataTable({
    processing: true,
    serverSide: true,
    ajax: {
      url: window.contractTypesRoutes.index,
      error: function(xhr, error, thrown) {
        console.error('DataTable AJAX error:', error);
        showToast('error', 'Có lỗi xảy ra khi tải dữ liệu');
      }
    },
    columns: [
      {
        data: 'name',
        name: 'name',
        render: function(data, type, row) {
          return `<strong>${data}</strong>`;
        }
      },
      {
        data: 'code',
        name: 'code',
        render: function(data, type, row) {
          return `<code class="text-primary">${data}</code>`;
        }
      },
      {
        data: 'description',
        name: 'description',
        render: function(data, type, row) {
          if (!data) return '<span class="text-muted">-</span>';
          return data.length > 50 ? data.substring(0, 50) + '...' : data;
        }
      },
      {
        data: 'templates_count',
        name: 'templates_count',
        orderable: false,
        className: 'text-center',
        render: function(data, type, row) {
          return `<span class="badge bg-label-info">${data}</span>`;
        }
      },
      {
        data: 'status_badge',
        name: 'is_active',
        orderable: false,
        className: 'text-center'
      },
      {
        data: 'sort_order',
        name: 'sort_order',
        className: 'text-center'
      },
      {
        data: 'action',
        name: 'action',
        orderable: false,
        searchable: false,
        className: 'text-center'
      }
    ],
    order: [[5, 'asc'], [0, 'asc']], // Sort by sort_order, then name
    language: {
      url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/vi.json'
    },
    responsive: true,
    pageLength: 25,
    lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
    dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
    drawCallback: function(settings) {
      // Add fade-in animation to new rows
      $('#contractTypesTable tbody tr').addClass('fade-in');
    }
  });
}

/**
 * Initialize event handlers
 */
function initializeEventHandlers() {
  // Form submission
  $('#contractTypeForm').on('submit', function(e) {
    e.preventDefault();
    saveContractType();
  });

  // Modal reset when hidden
  $('#contractTypeModal').on('hidden.bs.modal', function() {
    resetForm();
  });

  // Delete confirmation
  $('#confirmDeleteBtn').on('click', function() {
    if (deleteContractTypeId) {
      performDeleteContractType(deleteContractTypeId);
    }
  });

  // Form validation on input
  $('#contractTypeName, #contractTypeCode').on('input', function() {
    validateField(this);
  });

  // Auto-generate code from name
  $('#contractTypeName').on('input', function() {
    if (!isEditMode) {
      const name = $(this).val();
      const code = generateCodeFromName(name);
      $('#contractTypeCode').val(code);
    }
  });
}

/**
 * Save contract type (create or update)
 */
function saveContractType() {
  const form = document.getElementById('contractTypeForm');
  const submitBtn = form.querySelector('button[type="submit"]');

  // Show loading state
  setButtonLoading(submitBtn, true);

  // Clear previous validation errors
  clearValidationErrors();

  const formData = new FormData(form);
  const data = Object.fromEntries(formData.entries());

  // Convert checkbox to boolean
  data.is_active = document.getElementById('contractTypeIsActive').checked;

  const url = isEditMode ?
    window.contractTypesRoutes.update(data.id) :
    window.contractTypesRoutes.store;

  const method = isEditMode ? 'PUT' : 'POST';

  $.ajax({
    url: url,
    type: method,
    data: data,
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    },
    success: function(response) {
      setButtonLoading(submitBtn, false);

      if (response.success) {
        $('#contractTypeModal').modal('hide');
        contractTypesTable.ajax.reload(null, false); // Keep current page
        showToast('success', response.message);
      } else {
        showToast('error', response.message);
      }
    },
    error: function(xhr) {
      setButtonLoading(submitBtn, false);

      if (xhr.status === 422) {
        // Validation errors
        const errors = xhr.responseJSON.errors;
        displayValidationErrors(errors);
      } else {
        let message = 'Có lỗi xảy ra';

        if (xhr.responseJSON && xhr.responseJSON.message) {
          message = xhr.responseJSON.message;
        }

        showToast('error', message);
      }
    }
  });
}

/**
 * Edit contract type
 */
function editContractType(id) {
  isEditMode = true;
  $('#contractTypeModalTitle').text('Sửa loại hợp đồng');

  // Show loading in modal
  showModalLoading(true);

  $.ajax({
    url: window.contractTypesRoutes.show(id),
    type: 'GET',
    success: function(contractType) {
      showModalLoading(false);

      $('#contractTypeId').val(contractType.id);
      $('#contractTypeName').val(contractType.name);
      $('#contractTypeCode').val(contractType.code);
      $('#contractTypeDescription').val(contractType.description);
      $('#contractTypeSortOrder').val(contractType.sort_order);
      $('#contractTypeIsActive').prop('checked', contractType.is_active);

      $('#contractTypeModal').modal('show');
    },
    error: function(xhr) {
      showModalLoading(false);
      showToast('error', 'Không thể tải thông tin loại hợp đồng');
    }
  });
}

/**
 * Delete contract type
 */
function deleteContractType(id) {
  deleteContractTypeId = id;
  $('#deleteModal').modal('show');
}

/**
 * Perform delete contract type
 */
function performDeleteContractType(id) {
  const deleteBtn = document.getElementById('confirmDeleteBtn');
  setButtonLoading(deleteBtn, true);

  $.ajax({
    url: window.contractTypesRoutes.destroy(id),
    type: 'DELETE',
    headers: {
      'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
    },
    success: function(response) {
      setButtonLoading(deleteBtn, false);
      $('#deleteModal').modal('hide');

      if (response.success) {
        contractTypesTable.ajax.reload(null, false);
        showToast('success', response.message);
      } else {
        showToast('error', response.message);
      }
    },
    error: function(xhr) {
      setButtonLoading(deleteBtn, false);

      let message = 'Có lỗi xảy ra khi xóa loại hợp đồng';

      if (xhr.responseJSON && xhr.responseJSON.message) {
        message = xhr.responseJSON.message;
      }

      showToast('error', message);
      $('#deleteModal').modal('hide');
    }
  });
}

/**
 * Reset form to initial state
 */
function resetForm() {
  isEditMode = false;
  $('#contractTypeModalTitle').text('Thêm loại hợp đồng');
  $('#contractTypeForm')[0].reset();
  $('#contractTypeId').val('');
  $('#contractTypeIsActive').prop('checked', true);
  clearValidationErrors();
}

/**
 * Generate code from name
 */
function generateCodeFromName(name) {
  return name
    .toUpperCase()
    .replace(/[^A-Z0-9\s]/g, '')
    .replace(/\s+/g, '_')
    .substring(0, 20);
}

/**
 * Validate individual field
 */
function validateField(field) {
  const $field = $(field);
  const value = $field.val().trim();

  $field.removeClass('is-invalid is-valid');

  if (field.required && !value) {
    $field.addClass('is-invalid');
    return false;
  }

  if (value) {
    $field.addClass('is-valid');
  }

  return true;
}

/**
 * Display validation errors
 */
function displayValidationErrors(errors) {
  Object.keys(errors).forEach(field => {
    const $field = $(`#contractType${field.charAt(0).toUpperCase() + field.slice(1)}`);
    const $feedback = $field.siblings('.invalid-feedback');

    $field.addClass('is-invalid');

    if ($feedback.length) {
      $feedback.text(errors[field][0]);
    } else {
      $field.after(`<div class="invalid-feedback">${errors[field][0]}</div>`);
    }
  });
}

/**
 * Clear validation errors
 */
function clearValidationErrors() {
  $('#contractTypeForm .form-control').removeClass('is-invalid is-valid');
  $('#contractTypeForm .invalid-feedback').remove();
}

/**
 * Set button loading state
 */
function setButtonLoading(button, loading) {
  if (loading) {
    button.classList.add('btn-loading');
    button.disabled = true;
  } else {
    button.classList.remove('btn-loading');
    button.disabled = false;
  }
}

/**
 * Show/hide modal loading state
 */
function showModalLoading(show) {
  const modal = document.getElementById('contractTypeModal');
  const loadingHtml = `
    <div class="d-flex justify-content-center p-4">
      <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Đang tải...</span>
      </div>
    </div>
  `;

  if (show) {
    modal.querySelector('.modal-body').innerHTML = loadingHtml;
  }
}

/**
 * Show toast notification
 */
function showToast(type, message) {
  const toastId = 'toast-' + Date.now();
  const iconClass = type === 'success' ? 'ri-check-line' : 'ri-error-warning-line';
  const bgClass = type === 'error' ? 'danger' : type;

  const toastHtml = `
    <div id="${toastId}" class="toast align-items-center text-white bg-${bgClass} border-0" role="alert">
      <div class="d-flex">
        <div class="toast-body">
          <i class="${iconClass} me-2"></i>
          ${message}
        </div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
      </div>
    </div>
  `;

  let toastContainer = document.getElementById('toast-container');
  if (!toastContainer) {
    toastContainer = document.createElement('div');
    toastContainer.id = 'toast-container';
    toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
    toastContainer.style.zIndex = '9999';
    document.body.appendChild(toastContainer);
  }

  toastContainer.insertAdjacentHTML('beforeend', toastHtml);

  const toastElement = document.getElementById(toastId);
  const toast = new bootstrap.Toast(toastElement, {
    autohide: true,
    delay: type === 'error' ? 5000 : 3000
  });

  toast.show();

  toastElement.addEventListener('hidden.bs.toast', function() {
    toastElement.remove();
  });
}

// Export functions to global scope for DataTable buttons
window.editContractType = editContractType;
window.deleteContractType = deleteContractType;
