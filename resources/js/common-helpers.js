/**
 * Common Helper Functions for VPCC Project
 * Shared utilities across all modules
 */

/**
 * Show toast notification
 */
function showToast(type, message) {
  const toastId = 'toast-' + Date.now();
  const iconClass = type === 'success' ? 'ri-check-line' : 'ri-error-warning-line';
  const bgClass = type === 'error' ? 'danger' : type;

  const toastHtml = `
    <div id="${toastId}" class="toast align-items-center text-white bg-${bgClass} border-0" role="alert">
      <div class="d-flex">
        <div class="toast-body">
          <i class="${iconClass} me-2"></i>
          ${message}
        </div>
        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
      </div>
    </div>
  `;

  let toastContainer = document.getElementById('toast-container');
  if (!toastContainer) {
    toastContainer = document.createElement('div');
    toastContainer.id = 'toast-container';
    toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
    toastContainer.style.zIndex = '9999';
    document.body.appendChild(toastContainer);
  }

  toastContainer.insertAdjacentHTML('beforeend', toastHtml);

  const toastElement = document.getElementById(toastId);
  const toast = new bootstrap.Toast(toastElement, {
    autohide: true,
    delay: type === 'error' ? 5000 : 3000
  });

  toast.show();

  toastElement.addEventListener('hidden.bs.toast', function() {
    toastElement.remove();
  });
}

/**
 * Set button loading state
 */
function setButtonLoading(button, loading) {
  if (loading) {
    button.classList.add('btn-loading');
    button.disabled = true;
    // Add spinner if not exists
    if (!button.querySelector('.spinner-border')) {
      const spinner = document.createElement('span');
      spinner.className = 'spinner-border spinner-border-sm me-2';
      spinner.setAttribute('role', 'status');
      spinner.setAttribute('aria-hidden', 'true');
      button.insertBefore(spinner, button.firstChild);
    }
  } else {
    button.classList.remove('btn-loading');
    button.disabled = false;
    // Remove spinner
    const spinner = button.querySelector('.spinner-border');
    if (spinner) {
      spinner.remove();
    }
  }
}

/**
 * Clear validation errors
 */
function clearValidationErrors() {
  document.querySelectorAll('.form-control').forEach(field => {
    field.classList.remove('is-invalid', 'is-valid');
  });
  document.querySelectorAll('.invalid-feedback').forEach(feedback => {
    feedback.remove();
  });
}

/**
 * Display validation errors
 */
function displayValidationErrors(errors) {
  Object.keys(errors).forEach(fieldName => {
    // Convert field name to camelCase for ID matching
    const fieldId = fieldName.replace(/_([a-z])/g, (match, letter) => letter.toUpperCase());
    const field = document.getElementById(fieldId) || document.querySelector(`[name="${fieldName}"]`);
    
    if (field) {
      field.classList.add('is-invalid');
      
      // Remove existing feedback
      const existingFeedback = field.parentNode.querySelector('.invalid-feedback');
      if (existingFeedback) {
        existingFeedback.remove();
      }
      
      // Add new feedback
      const feedback = document.createElement('div');
      feedback.className = 'invalid-feedback';
      feedback.textContent = errors[fieldName][0];
      field.parentNode.appendChild(feedback);
    }
  });
}

/**
 * Validate individual field
 */
function validateField(field) {
  const $field = $(field);
  const value = $field.val().trim();
  
  $field.removeClass('is-invalid is-valid');
  
  if (field.required && !value) {
    $field.addClass('is-invalid');
    return false;
  }
  
  if (value) {
    $field.addClass('is-valid');
  }
  
  return true;
}

/**
 * Check if routes object is defined
 */
function checkRoutes(routesObject, moduleName) {
  if (typeof routesObject === 'undefined') {
    console.error(`${moduleName} routes not defined`);
    showToast('error', `Lỗi cấu hình: Routes ${moduleName} không được định nghĩa`);
    return false;
  }
  return true;
}

/**
 * Format currency (VND)
 */
function formatCurrency(amount) {
  return new Intl.NumberFormat('vi-VN', {
    style: 'currency',
    currency: 'VND'
  }).format(amount);
}

/**
 * Format date
 */
function formatDate(dateString, format = 'dd/MM/yyyy') {
  if (!dateString) return '';
  
  const date = new Date(dateString);
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  
  switch (format) {
    case 'dd/MM/yyyy':
      return `${day}/${month}/${year}`;
    case 'yyyy-MM-dd':
      return `${year}-${month}-${day}`;
    default:
      return dateString;
  }
}

/**
 * Debounce function
 */
function debounce(func, wait, immediate) {
  let timeout;
  return function executedFunction() {
    const context = this;
    const args = arguments;
    const later = function() {
      timeout = null;
      if (!immediate) func.apply(context, args);
    };
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    if (callNow) func.apply(context, args);
  };
}

/**
 * Confirm delete action
 */
function confirmDelete(message = 'Bạn có chắc chắn muốn xóa?') {
  return new Promise((resolve) => {
    if (typeof Swal !== 'undefined') {
      Swal.fire({
        title: 'Xác nhận xóa',
        text: message,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Xóa',
        cancelButtonText: 'Hủy'
      }).then((result) => {
        resolve(result.isConfirmed);
      });
    } else {
      resolve(confirm(message));
    }
  });
}

// Export functions to global scope
window.showToast = showToast;
window.setButtonLoading = setButtonLoading;
window.clearValidationErrors = clearValidationErrors;
window.displayValidationErrors = displayValidationErrors;
window.validateField = validateField;
window.checkRoutes = checkRoutes;
window.formatCurrency = formatCurrency;
window.formatDate = formatDate;
window.debounce = debounce;
window.confirmDelete = confirmDelete;
